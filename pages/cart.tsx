import {GetServerSideProps} from 'next';
import Cookies from 'cookies';
import {DynamicPage} from '@core/types';
import {erpClient, initDynamicPageParams} from '@core/helpers/server';
import Cart from '@components/pages/store/Cart';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound} = await initDynamicPageParams(ctx, {
        isSecure: false
    });

    const cookies = new Cookies(ctx.req, ctx.res);
    const cartId = cookies.get('cart-id');
    // @ts-ignore
    const customerId = props.session?.user.id;

    if (typeof cartId === 'string' && !!cartId) {
        let cart = null;

        try {
            cart = await erpClient.post('cart', {cartId, reInit: true});
        } catch (error: any) {}

        if (!!cart) {
            props.cart = cart;
        }
    } else if (typeof customerId === 'string') {
        let cart = null;

        try {
            cart = await erpClient.post('cart', {customerId, reInit: true});
        } catch (error: any) {}

        if (!!cart) {
            props.cart = cart;
        }
    }

    return {
        props: {
            ...props,
            redirect: ctx.query.redirect ?? ''
        },
        notFound
    };
};

const CartPage: DynamicPage<typeof getServerSideProps> = props => {
    // @ts-ignore
    return <Cart {...props} />;
};

CartPage.layout = 'default';

export default CartPage;
