import {GetServerSideProps} from 'next';
import storeConfig from '~/store.config';
import {DynamicPage} from '@core/types';
import {erpClient, initDynamicPageParams} from '@core/helpers/server';
import Auth from '@components/pages/auth/Auth';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound} = await initDynamicPageParams(ctx, {
        isSecure: false
    });

    // Check the user is already signed in.
    if (!!props.session) {
        return {
            props,
            notFound,
            redirect: {
                destination: ctx.query.redirect ?? '/',
                permanent: false
            }
        };
    }

    // Get locale.
    const locale = ctx.locale ?? storeConfig.defaultLocale;

    // Get terms of membership text.
    props.termsOfMembershipText = (
        await erpClient.post('common/text', {
            type: 'terms-of-membership-text',
            locale
        })
    ).content;

    // Get clarification text.
    props.clarificationText = (
        await erpClient.post('common/text', {
            type: 'clarification-text',
            locale
        })
    ).content;

    return {
        props: {
            ...props,
            redirect: ctx.query.redirect ?? ''
        },
        notFound
    };
};

const AuthPage: DynamicPage<typeof getServerSideProps> = props => {
    // @ts-ignore
    return <Auth {...props} />;
};

AuthPage.layout = 'auth';

export default AuthPage;
