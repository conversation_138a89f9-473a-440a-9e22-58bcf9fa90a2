import {GetStaticPropsContext} from 'next';
import storeConfig from '~/store.config';
import {base64, parseURLSearchParams, trim} from '../';
import {
    Brand,
    Breadcrumb,
    Campaign,
    ContentPage,
    NavigationItem,
    PageProps,
    Product,
    SpecialPageProducts
} from '../../types';
import {erpClient, loadTranslations} from './';

type ParsedUrlQuery = Record<string, string | string[] | undefined>;

type PageParams<T = {}> = {
    props: PageProps & T;
    revalidate?: number;
    notFound: boolean;
};

export default async function initStaticPageParams<T = {}>(
    ctx: GetStaticPropsContext
): Promise<PageParams<T>> {
    const locale = ctx.locale ?? storeConfig.defaultLocale;
    const props: PageProps = {
        storeInfo: await erpClient.post('common/info', {
            locale
        }),
        pageType: 'page',
        ...(await loadTranslations(locale))
    };
    let revalidate = 60;

    // Get navigation.
    const navigation = props.storeInfo.navigation;

    // Get slug and page number.
    const {slug, pageNumber} = processParams(ctx.params);

    // Check if slug is exists else we on home page.
    if (!slug) {
        // Set page type.
        props.pageType = 'home';

        const productCatalogs = navigation.filter(
            item =>
                item.depth === 0 &&
                item.type === 'product-catalog' &&
                !item.showInMainMenu
        );

        let productCatalogMap: SpecialPageProducts = {};

        if (Array.isArray(productCatalogs) && productCatalogs.length > 0) {
            for (const productCatalog of productCatalogs) {
                const result = await erpClient.post('catalog/products', {
                    categoryPaths: productCatalog.productCategoryPaths,
                    groupIds: productCatalog.productGroupIds,
                    brandIds: productCatalog.productBrandIds,
                    tagIds: productCatalog.productTagIds,
                    advancedFilters: productCatalog.advancedFilters,
                    fields: storeConfig.catalog.productListItemFields,
                    set: productCatalog.productSet,
                    skip: 0,
                    limit: 12,
                    sort: storeConfig.catalog.productSort
                        ? storeConfig.catalog.productSort
                        : {createdAt: -1},
                    paginated: false
                });

                productCatalogMap[productCatalog.id] = {
                    products: result.products ?? [],
                    catalogName: productCatalog.name,
                    detailPageLink: productCatalog.slug
                };
            }
            props.productCatalogMap = productCatalogMap;
        }

        return {props: props as any, revalidate, notFound: false};
    }

    // Set slug.
    props.slug = slug;

    // Set page number.
    if (typeof pageNumber === 'number') {
        props.pageNumber = pageNumber;
    }

    // Get navigation item.
    let navigationItem = navigation.find(
        navigationItem =>
            navigationItem.slug === slug &&
            (navigationItem.type === 'product-catalog' ||
                navigationItem.type === 'page' ||
                navigationItem.type === 'link')
    );

    // Set navigation item.
    if (typeof navigationItem !== 'undefined') {
        props.navigationItem = navigationItem;
    }

    // Check navigation item.
    if (typeof navigationItem === 'undefined') {
        // Get product.
        try {
            let processedSlug = slug;
            let selectedAttributes = null;
            if (slug.indexOf('-') !== -1) {
                try {
                    const parts = processedSlug.split('-');
                    const lastPart = parts[parts.length - 1];
                    const decoded = base64.decode(lastPart);

                    if (decoded.length > 0 && decoded.includes('=')) {
                        const parsed = parseURLSearchParams(decoded);

                        if (Object.keys(parsed).length > 0 && !!parsed._es) {
                            delete parsed._es;
                            processedSlug = parts.slice(0, -1).join('-');
                            selectedAttributes = parsed;
                        }
                    }
                } catch (error) {}
            }

            let product: Product | null = null;
            let productBreadcrumbs: Breadcrumb[] | null = null;
            let productCampaigns: Campaign[] | null = null;

            try {
                const result = await erpClient.post('catalog/product', {
                    slug: processedSlug
                });

                product = result.product;
                productBreadcrumbs = result.breadcrumbs;
                productCampaigns = result.campaigns;
            } catch (error) {}

            if (!!product) {
                // Set page type.
                props.pageType = 'product';

                // Set selected attributes.
                if (selectedAttributes !== null) {
                    props.selectedAttributes = selectedAttributes;
                }

                // Set product.
                props.product = product;

                try {
                    const result = await erpClient.post('catalog/products', {
                        set: 'related-products',
                        productId: product.productId,
                        fields: storeConfig.catalog.productListItemFields,
                        skip: 0,
                        limit: 12,
                        sort: storeConfig.catalog.productSort
                            ? storeConfig.catalog.productSort
                            : {createdAt: -1},
                        paginated: false
                    });

                    props.relatedProducts = result.products;
                } catch (error) {
                    props.relatedProducts = [];
                }

                // Breadcrumbs.
                if (
                    !!productBreadcrumbs &&
                    Array.isArray(productBreadcrumbs) &&
                    productBreadcrumbs.length > 0
                ) {
                    props.breadcrumbs = [
                        {name: 'Home', slug: '', href: '/'},
                        ...(productBreadcrumbs.map((productBreadcrumb: any) => {
                            if (!!productBreadcrumb.id) {
                                productBreadcrumb.href = `/${productBreadcrumb.slug}`;
                            }

                            return productBreadcrumb;
                        }) ?? [])
                    ];
                }

                // Campaigns.
                props.campaigns = productCampaigns ?? [];

                return {props: props as any, revalidate, notFound: false};
            }
        } catch (error) {}

        // Get brands.
        let brand: Brand | null = null;
        try {
            brand = await erpClient.post('catalog/brand', {
                slug
            });
        } catch (error) {}

        if (!!brand) {
            // Set page type.
            props.pageType = 'catalog';

            // Set brand.
            props.brand = brand;

            // Navigation item.
            const navItem: NavigationItem = {
                id: brand.id,
                type: 'product-catalog',
                name: brand.name,
                slug: brand.slug,
                locale,
                showInMainMenu: false,
                seoTitle: brand.seoTitle ?? brand.name,
                seoDescription: brand.seoDescription ?? '',
                content: brand.content ?? '',
                productCategoryPaths: [],
                attachments:[],
                productGroupIds: [],
                productBrandIds: [brand.id],
                path: '',
                href: `/${trim(brand.slug, '/')}`,
                order: 99999
            };
            const existing = navigation.find(
                navigationItem =>
                    navigationItem.slug === navItem.slug &&
                    (navigationItem.type === 'product-catalog' ||
                        navigationItem.type === 'page' ||
                        navigationItem.type === 'link')
            );
            if (typeof existing === 'undefined') {
                navigation.push(navItem);
                navigationItem = navItem;
            } else {
                navigationItem = existing;
            }

            // Set navigation.
            props.storeInfo.navigation = navigation;

            // Set navigation item.
            props.navigationItem = navigationItem;
        } else {
            return {props: props as any, revalidate, notFound: true};
        }
    }

    if (typeof navigationItem !== 'undefined') {
        // Get page.
        if (navigationItem.type === 'page') {
            // Check page id.
            if (!navigationItem.pageId) {
                return {props: props as any, revalidate, notFound: false};
            }

            // Get page.
            let page: ContentPage | null = null;
            try {
                page = await erpClient.post('common/page', {
                    pageId: navigationItem.pageId
                });
            } catch (error) {}

            // Check page.
            if (!page) {
                return {props: props as any, notFound: true};
            }

            // Set page type.
            props.pageType = 'page';

            // Breadcrumbs.
            props.breadcrumbs = getBreadcrumbsForNavigationItem(
                navigation,
                navigationItem
            );

            // Side navigation.
            props.sideNavigation = getSideNavigationForNavigationItem(
                navigation,
                navigationItem
            );

            // Set page.
            props.page = page;

            return {props: props as any, revalidate, notFound: false};
        }

        // Get catalog.
        if (navigationItem.type === 'product-catalog') {
            // Get products.
            let result: any = null;

            // Check if the current catalog is special page.
            const subNavItems = navigation.filter(
                item =>
                    item.slug.startsWith(`${slug}/`) &&
                    item.depth === navigationItem?.depth! + 1 &&
                    !item.showInMainMenu
            );
            const isSpecialPage = subNavItems.length > 0;

            if (isSpecialPage) {
                const productCatalogMap: SpecialPageProducts = {};

                for (const subNavItem of subNavItems.filter(
                    item => item.type === 'product-catalog'
                )) {
                    const result = await erpClient.post('catalog/products', {
                        categoryPaths: subNavItem.productCategoryPaths,
                        attachments:subNavItem.attachments,
                        groupIds: subNavItem.productGroupIds,
                        brandIds: subNavItem.productBrandIds,
                        set: subNavItem.productSet,
                        tagIds: subNavItem.productTagIds,
                        advancedFilters: subNavItem.advancedFilters,
                        fields: storeConfig.catalog.productListItemFields,
                        skip: 0,
                        limit: 12,
                        sort: storeConfig.catalog.productSort
                            ? storeConfig.catalog.productSort
                            : {createdAt: -1},
                        paginated: false
                    });

                    productCatalogMap[subNavItem.id] = {
                        products: result.products ?? [],
                        catalogName: subNavItem.name,
                        detailPageLink: subNavItem.slug
                    };
                }

                props.isSpecialPage = true;
                props.pageType = 'catalog';
                props.hasNextPage = false;
                props.totalProductCountText = '0';
                props.filters = [];
                props.products = [];
                props.breadcrumbs = getBreadcrumbsForNavigationItem(
                    navigation,
                    navigationItem
                );
                props.productCatalogMap = productCatalogMap;

                return {props: props as any, revalidate, notFound: false};
            } else {
                // Set special page
                props.isSpecialPage = false;

                // Get products
                try {
                    result = await erpClient.post('catalog/products', {
                        categoryPaths: navigationItem.productCategoryPaths,
                        attachments: navigationItem.attachments,
                        groupIds: navigationItem.productGroupIds,
                        brandIds: navigationItem.productBrandIds,
                        set: navigationItem.productSet,
                        tagIds: navigationItem.productTagIds,
                        advancedFilters: navigationItem.advancedFilters,
                        fields: storeConfig.catalog.productListItemFields,
                        skip:
                            typeof pageNumber === 'number'
                                ? (storeConfig.catalog.productsPerPage || 48) *
                                  (pageNumber - 1)
                                : 0,
                        limit: storeConfig.catalog.productsPerPage || 48,
                        sort: storeConfig.catalog.productSort
                            ? storeConfig.catalog.productSort
                            : {createdAt: -1},
                        paginated: true
                    });
                } catch (error) {}

                if (!!result) {
                    // Set page type.
                    props.pageType = 'catalog';

                    // Set products.
                    props.products = result.products;
                    props.hasNextPage = result.hasNextPage;
                    props.totalProductCountText = result.totalProductCountText;

                    // Get filters.
                    let filters: any = null;
                    try {
                        filters = await erpClient.post('catalog/filters', {
                            categoryPaths: navigationItem.productCategoryPaths,
                            attachments:navigationItem.attachments,
                            groupIds: navigationItem.productGroupIds,
                            brandIds: navigationItem.productBrandIds,
                            set: navigationItem.productSet,
                            tagIds: navigationItem.productTagIds,
                            advancedFilters: navigationItem.advancedFilters
                        });
                    } catch (error) {}

                    // Set filters.
                    if (!!filters) {
                        props.filters = filters;
                    }

                    // Breadcrumbs.
                    props.breadcrumbs = getBreadcrumbsForNavigationItem(
                        navigation,
                        navigationItem
                    );

                    return {props: props as any, revalidate, notFound: false};
                }
            }
        }

        // Get custom page.
        if (navigationItem.type === 'link') {
            // Set page type.
            props.pageType = 'custom';

            // Breadcrumbs.
            props.breadcrumbs = getBreadcrumbsForNavigationItem(
                navigation,
                navigationItem
            );

            return {props: props as any, revalidate, notFound: false};
        }
    }

    return {props: props as any, revalidate, notFound: true};
}

function processParams(params: ParsedUrlQuery | undefined) {
    if (typeof params !== 'undefined') {
        if (Array.isArray(params.slug) && params.slug.length > 0) {
            const slug = params.slug;

            if (slug[slug.length - 2] === 'page') {
                const page = parseInt(slug[slug.length - 1]);

                if (!isNaN(page)) {
                    return {
                        slug: slug.slice(0, params.slug.length - 2).join('/'),
                        pageNumber: page
                    };
                }
            }

            return {slug: slug.join('/')};
        } else if (typeof params.slug === 'string' && params.slug.length > 0) {
            return {slug: params.slug};
        }
    }

    return {};
}

function getBreadcrumbsForNavigationItem(
    navigation: NavigationItem[],
    navigationItem: NavigationItem
) {
    const slugParts = navigationItem.slug.split('/');
    const breadcrumbs: Breadcrumb[] = [{name: 'Home', slug: '', href: '/'}];
    const currentPaths: string[] = [];

    for (const slugPart of slugParts) {
        currentPaths.push(slugPart);

        const currentNavigationItem = navigation.find(
            currentNavigationItem =>
                currentNavigationItem.slug === currentPaths.join('/') &&
                (navigationItem.type === 'product-catalog' ||
                    navigationItem.type === 'page' ||
                    navigationItem.type === 'link')
        );

        if (typeof currentNavigationItem === 'object') {
            const isCurrent = navigationItem.id === currentNavigationItem.id;

            breadcrumbs.push({
                id: currentNavigationItem.id,
                name: currentNavigationItem.name,
                slug: currentPaths.join('/'),
                ...(!isCurrent ? {href: `/${currentPaths.join('/')}`} : {})
            });
        }
    }

    return breadcrumbs;
}

function getSideNavigationForNavigationItem(
    navigation: NavigationItem[],
    navigationItem: NavigationItem
) {
    const slugParts = navigationItem.slug.split('/');
    const items: (NavigationItem & {isActive: boolean})[] = [];

    if (slugParts.length < 2) {
        return items;
    }

    const parentSlug = slugParts.slice(0, slugParts.length - 1).join('/');
    for (const currentNavigationItem of navigation) {
        const subSlugParts = currentNavigationItem.slug.split('/');

        if (
            subSlugParts.length > 1 &&
            subSlugParts.slice(0, slugParts.length - 1).join('/') === parentSlug
        ) {
            items.push({
                ...currentNavigationItem,
                isActive: currentNavigationItem.slug === navigationItem.slug
            });
        }
    }

    return items;
}
