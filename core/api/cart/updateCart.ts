import Cookies from 'cookies';
import {trim} from '@core/helpers';
import {endpoint, erpClient} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const cookies = new Cookies(req, res);

    let cartId: string | undefined = trim(cookies.get('cart-id') || '');
    if (cartId === '') {
        cartId = undefined;
    }

    const {orderNote} = req.body;
    if (typeof orderNote !== 'string' || orderNote.length < 3) {
        throw new Error(
            'The order note is invalid. Please make sure you have entered correct information!'
        );
    }

    const cart = await erpClient.post('cart/update-cart', {
        cartId,
        customerId: ctx.customer?.id,
        orderNote
    });

    res.json(cart);
});
