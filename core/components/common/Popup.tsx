import {UiButton, UiDialog, UiImage, UiTransition} from '@core/components/ui';
import {cls} from '@core/helpers';
import {useStore} from '@core/hooks';
import {XIcon} from '@core/icons/outline';
import {useRouter} from 'next/router';
import React, {Fragment, useState, useEffect} from 'react';

const Popup = () => {
    const {navigation} = useStore();
    const router = useRouter();

    const [isShown, setIsShown] = useState(false);
    const popupData = navigation?.find(nav => nav.type === 'popup');

    useEffect(() => {
        const popupAlreadyShown = localStorage.getItem('popupShown');
        if (
            !popupAlreadyShown &&
            popupData &&
            popupData.images &&
            popupData.images.length > 0
        )
            setIsShown(true);
    }, []);

    const closePopup = () => {
        setIsShown(false);
        localStorage.setItem('popupShown', 'true');
    };

    const navigateToPage = () => {
        closePopup();
        if (popupData?.link) router.push(popupData.link);
    };

    return (
        <UiTransition.Root show={isShown} as={Fragment}>
            <UiDialog
                as="div"
                className="fixed inset-0 top-1/4 z-[9999] overflow-y-auto  px-8 lg:top-1 lg:px-0"
                onClose={closePopup}
            >
                <div className="flex text-center sm:block sm:px-6 xl:px-8">
                    <UiTransition.Child
                        as={Fragment}
                        enter="transition duration-300"
                        enterFrom="opacity-0"
                        enterTo="opacity-100"
                        leave="transition duration-200"
                        leaveFrom="opacity-100"
                        leaveTo="opacity-0"
                    >
                        <UiDialog.Overlay
                            onClick={closePopup}
                            className="fixed inset-0 bg-gray-900 bg-opacity-20"
                        />
                    </UiTransition.Child>

                    <span
                        className="hidden sm:inline-block sm:h-screen sm:align-middle"
                        aria-hidden="true"
                    >
                        &#8203;
                    </span>

                    <UiTransition.Child
                        as={Fragment}
                        enter="transition duration-300 transform"
                        enterFrom="opacity-0 scale-105"
                        enterTo="opacity-100 scale-100"
                        leave="transition duration-200 transform"
                        leaveFrom="opacity-100 scale-100"
                        leaveTo="opacity-0 scale-105"
                    >
                        <div
                            className={cls(
                                'relative flex transform  text-left text-base transition sm:my-8 sm:inline-block sm:align-middle'
                            )}
                        >
                            <div className="relative flex w-full flex-col items-stretch overflow-hidden rounded-lg bg-white">
                                <UiButton
                                    className="absolute right-0 z-[99]  m-3 flex items-center justify-center rounded-full bg-white p-0 text-gray-400 transition duration-75 ease-out hover:bg-gray-200 hover:text-gray-500 focus:border-transparent focus:ring-transparent"
                                    onClick={closePopup}
                                >
                                    <XIcon className="h-5 w-5" />
                                </UiButton>
                                <UiButton
                                    onClick={navigateToPage}
                                    className="h-full w-full border-0 p-0"
                                >
                                    <UiImage
                                        src={popupData?.images?.[0] || ''}
                                        alt={popupData?.name || 'Popup image'}
                                        width={600}
                                        height={600}
                                        fit="cover"
                                    />
                                </UiButton>
                            </div>
                        </div>
                    </UiTransition.Child>
                </div>
            </UiDialog>
        </UiTransition.Root>
    );
};

export default Popup;
