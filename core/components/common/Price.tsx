import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useStore} from '@core/hooks';
import {UiPrice, UiPriceProps} from '@core/components/ui';

export type PriceProps = Omit<UiPriceProps, 'locale' | 'currency'>;

const Price: FC<PriceProps> = memo(props => {
    const {as = 'span', ...rest} = props;
    const {locale, currency} = useStore();

    return <UiPrice locale={locale} currency={currency} as={as} {...rest} />;
});

if (isDev) {
    Price.displayName = 'Price';
}

export default Price;
