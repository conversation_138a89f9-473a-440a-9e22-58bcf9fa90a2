import {FC, memo, useMemo} from 'react';
import {NavigationItem} from '@core/types';
import {isDev} from '@core/helpers';
import {useStore} from '@core/hooks';
import {UiImage, UiLink, UiSlider} from '@core/components/ui';
import {Autoplay} from '@core/components/ui/Slider';

interface StorySliderProps {
    forSpecialPage?: boolean;
    items?: NavigationItem[];
}

const StorySlider: FC<StorySliderProps> = memo(
    ({forSpecialPage = false, items}) => {
        return <div></div>;
    }
);

if (isDev) {
    StorySlider.displayName = 'StorySlider';
}

export default StorySlider;
