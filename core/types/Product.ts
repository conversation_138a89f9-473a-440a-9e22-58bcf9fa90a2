import type ProductFeature from './ProductFeature';
import type ProductOption from './ProductOption';
import type ProductVariant from './ProductVariant';


type AlternateProductItem = {
    productId: string;
    images?: string;
    name: string;
    description: string;
    slug: string;
    salesPrice: number;
    unDiscountedSalesPrice: number;
    discount: number;
    quantity: number;
    rating: number;
    code: string;
};

type Product = {
    storeId: string;
    productId: string;
    isConfigurable: boolean;
    code: string;
    name: string;
    definition: string;
    slug: string;
    seoTitle?: string;
    seoDescription?: string;
    barcode?: string;
    categoryId: string;
    categoryName: string;
    categoryPath: string;
    groupIds: string[];
    groupNames: string[];
    brandId?: string;
    brandName?: string;
    brandSlug?: string;
    shortDescription?: string;
    description?: string;
    manufacturer?: string;
    manufacturerProductCode?: string;
    rating: number;
    reviewCount: number;
    reviews: Record<string, any>[];
    featuredReview?: Record<string, any>;
    reviewsReport: {starCount: number; count: number; percentage: number}[];
    salesCount: number;
    favoritesCount: number;
    visitsCount: number;
    images?: string[];
    isSuggestedProduct?: boolean;
    isBestSellingProduct?: boolean;
    isNewProduct?: boolean;
    isDiscountedProduct?: boolean;
    isAdultProduct?: boolean;
    isKitProduct?: boolean;
    isPCMFinder?: boolean;
    unitId: string;
    unitName: string;
    deliveryOptionIds: string[];
    estimatedDeliveryDuration?: number;
    deliveryAtSpecifiedDate?: boolean;
    deliveryAtSpecifiedTime?: boolean;
    weight: number;
    height: number;
    width: number;
    depth: number;
    salesPrice: number;
    unDiscountedSalesPrice: number;
    discount: number;
    hasDiscount: boolean;
    hasFreeShipping?: boolean;
    quantity: number;
    isFake?: boolean;
    features: ProductFeature[];
    variants?: ProductVariant[];
    options: ProductOption[];
    variantImages?: {
        attributeCode: string;
        attributeValue: string;
        images: string[];
    }[];
    link?: string;
    isPCMProduct?: boolean;
    pcmModelId?: string;
    pcmPayload?: any;
    pcmPayloadInitialValues?: any;
    salesPriceAtCart?: number;
    attachments?: {
        attachmentId: string;
        fileName: string;
        previewUrl: string;
        extension: string;
    }[];
    tags?: {
        tagId: string;
        name: string;
        description: string;
        images: string[];
    }[];
    additionalInformations?: {
        code: string;
        label: string;
        value: string;
    }[];
    kitConfigurationSteps?: {
        stepId: string;
        code: string;
        name: string;
        tab: string;
        items: {
            id: string;
            productId: string;
            productDefinition: string;
            productCode: string;
            productImage: string;
            quantity: number;
            unitPrice: number;
            unitId: string;
            stepId: string;
            isDefault: boolean;
            storeInfo?: {
                slug: string;
                rating: number;
                reviewCount: number;
                favoritesCount: number;
            };
        }[];
    }[];
    kitProductBrands?: {
        brandId: string;
        brandName: string;
        brandCode: string;
        brandSlug: string;
        brandImages: string[];
    }[];
    darkMode?: boolean;
    bannerSlug?: string;
    bannerImages?: string[];
    availableQuantity?: number;
    productStockQuantity?: number;
    discountedPrice?: number;
    campaigns?: {
        title: string;
        description: string;
        endDate: string;
        id: string;
        startDate: string;
        type: string;
    }[];
    alternateProducts?: AlternateProductItem[];
};

export default Product;
