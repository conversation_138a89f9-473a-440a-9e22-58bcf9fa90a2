type NavigationItem = {
    id: string;
    type:
        | 'product-catalog'
        | 'page'
        | 'collection'
        | 'slide'
        | 'link'
        | 'story'
        | 'popup'
        | 'topbar-banner'
        | 'file-catalog';
    name: string;
    slug: string;
    link?: string;
    pageId?: string;
    depth?: number;

    locale: string;
    showInMainMenu: boolean;
    svgIcon?: string;

    seoTitle: string;
    seoDescription: string;

    productCategoryPaths: string[];
    attachments:{
        id:string;
        url:string;
        name:string;
    }[];
    productGroupIds: string[];
    productBrandIds: string[];
    productSet?: string;
    productTagIds?: string[];
    advancedFilters?: Record<string, any>;

    images?: string[];

    banners?: {
        id: string;
        name: string;
        link: string;
        images: string[];
        isActive: boolean;
    }[];

    content?: string;
    section?: string;
    description?: string;
    darkMode?: boolean;
    path: string;
    order: number;

    href: string;
    hasChild?: boolean;
};

export default NavigationItem;
