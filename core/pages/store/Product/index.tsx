import {memo, useEffect} from 'react';
import {
    Breadcrumb,
    Campaign,
    Page,
    Product,
    ProductListItem
} from '@core/types';
import {isDev, pushIntoGTMDataLayer} from '@core/helpers';
import {useMobile, useStore} from '@core/hooks';
import Actions from './Actions';
import Breadcrumbs from './Breadcrumbs';
import Configurator from './Configurator';
import {ProductProvider} from './context';
import ImageGallery from './ImageGallery';
import Info from './Info';
import Meta from './Meta';
import MobileDetail from './MobileDetail';
import Options from './Options';
import ProductInformation from './ProductInformation';
import ProductReviews from './ProductReviews';
import RelatedProducts from './RelatedProducts';
import SideBar from './SideBar';
import Stats from './Stats';
import Tabs from './Tabs';
import AdultConsent from './AdultConsent';
import InstallmentOptions from './InstallmentOptions';
import ComparisonList from './ComparisonList';
import {useRouter} from 'next/router';
import AlternateProducts from './AlternateProducts';

type ProductPageProps = {
    slug: string;
    breadcrumbs: Breadcrumb[];
    campaigns: Campaign[];
    product: Product;
    selectedAttributes?: Record<string, any>;
    relatedProducts: ProductListItem[];
};

const ProductPage: Page<ProductPageProps> = memo(props => {
    const {
        slug,
        breadcrumbs,
        campaigns,
        product,
        selectedAttributes,
        relatedProducts
    } = props;
    const {isMobile} = useMobile();
    const {currency} = useStore();
    const router = useRouter();
    const variantItem = product.variants?.filter(
        variant => '/' + variant.slug === router.asPath
    )[0];

    useEffect(() => {
        // ---------- Google Tag Manager ----------
        pushIntoGTMDataLayer({
            event: 'view_item',
            data: {
                currency: currency.name === 'TL' ? 'TRY' : currency.name,
                value: product.unDiscountedSalesPrice
                    ? product.unDiscountedSalesPrice
                    : product.salesPrice,
                items: [
                    {
                        item_id: product.code,
                        item_name: product.name,
                        discount:
                            product.unDiscountedSalesPrice > 0
                                ? product.unDiscountedSalesPrice -
                                  product.salesPrice
                                : 0,
                        item_brand: product.brandName,
                        item_category: product.categoryName,
                        price: product.unDiscountedSalesPrice
                            ? product.unDiscountedSalesPrice
                            : product.salesPrice
                    }
                ]
            }
        });
        // ----------------------------------------
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        const previouslyItems = localStorage.getItem('lastViewed');
        let previouslyItemsArray: {
            productId: string;
            productName: string;
            price: number;
            discountedPrice: number | undefined;
            quantity: number;
            productSlug: string;
            productImage: string;
            link: string;
            code?: string;
            hasDiscount?: boolean;
            productStockQuantity?: number;
        }[] = previouslyItems ? JSON.parse(previouslyItems) : [];

        const currentItem = variantItem
            ? {
                  productId: variantItem.productId,
                  productName: variantItem.name,
                  price: variantItem.salesPrice,
                  discountedPrice: variantItem.unDiscountedSalesPrice,
                  quantity: variantItem.quantity,
                  productSlug: variantItem.slug,
                  productImage: variantItem.images?.[0] || '',
                  link: variantItem.slug || '',
                  code: variantItem.code,
                  hasDiscount: variantItem.hasDiscount,
                  productStockQuantity: variantItem.quantity
              }
            : {
                  productId: product.productId,
                  productName: product.name,
                  price: product.salesPrice,
                  discountedPrice: product.unDiscountedSalesPrice,
                  quantity: product.quantity,
                  productSlug: product.slug,
                  productImage: product.images?.[0] || '',
                  link: product.link || '',
                  code: product.code,
                  hasDiscount: product.hasDiscount,
                  productStockQuantity: product.productStockQuantity
              };

        const existingIndex = previouslyItemsArray.findIndex(
            (previouslyItem: {productId: string}) =>
                previouslyItem.productId === currentItem.productId
        );

        if (existingIndex > -1) {
            previouslyItemsArray.splice(existingIndex, 1);
        }

        previouslyItemsArray.unshift(currentItem);

        if (previouslyItemsArray.length > 10) {
            previouslyItemsArray.pop();
        }

        localStorage.setItem(
            'lastViewed',
            JSON.stringify(previouslyItemsArray)
        );

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [product, variantItem]);

    return (
        <ProductProvider
            slug={slug}
            product={product}
            selectedAttributes={selectedAttributes}
            relatedProducts={relatedProducts}
            campaigns={campaigns}
        >
            <Meta />

            <div className="container relative hidden xl:block">
                {breadcrumbs && <Breadcrumbs breadcrumbs={breadcrumbs} />}

                <div className="xl:py-8">
                    {product.isPCMProduct && !isMobile ? (
                        <Configurator />
                    ) : (
                        <div className="flex">
                            <div className="flex-1 pt-4 xl:pt-0">
                                <div className="grid grid-cols-1 xl:grid-cols-12 xl:gap-8 xl:rounded xl:border xl:border-gray-200 xl:p-4 xl:shadow-sm">
                                    <div className="xl:col-span-6">
                                        <ImageGallery />
                                    </div>

                                    <div className="mt-8 xl:col-span-6 xl:mt-0">
                                        <Info />

                                        {product.isConfigurable &&
                                            (product.variants ?? []).length >
                                                0 && <Options />}

                                        <Actions />

                                        <Stats />
                                    </div>
                                </div>
                            </div>

                            <div className="ml-8 hidden w-60 xl:block">
                                <SideBar />
                            </div>
                        </div>
                    )}

                    <Tabs />
                    <ProductInformation />
                    <ProductReviews />
                    <InstallmentOptions />
                    <RelatedProducts />
                    {product.alternateProducts &&
                        product.alternateProducts.length > 0 && (
                            <AlternateProducts
                                alternateProducts={
                                    product.alternateProducts as []
                                }
                            />
                        )}
                </div>
            </div>

            <ComparisonList />

            {!product.isPCMProduct && (
                <div className="fixed inset-0 z-50 block bg-white xl:hidden">
                    {isMobile && <MobileDetail />}
                </div>
            )}
            {!!product.isPCMProduct && (
                <div className="fixed inset-0 z-50 block overflow-y-auto bg-white xl:hidden">
                    {isMobile && <Configurator />}
                </div>
            )}
            {product.isAdultProduct && <AdultConsent />}
        </ProductProvider>
    );
});

if (isDev) {
    ProductPage.displayName = 'ProductPage';
}

export default ProductPage;
