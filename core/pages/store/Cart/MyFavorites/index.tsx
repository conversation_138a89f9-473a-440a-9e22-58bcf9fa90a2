import {FC, memo, useCallback, useEffect, useRef, useState} from 'react';
import {ProductListItem} from '@core/types';
import {cls, isDev, jsonRequest, randomId} from '@core/helpers';
import {useIntersection, useStore, useTrans} from '@core/hooks';
import {UiImage, UiLink, notification} from '@core/components/ui';
import {HeartIcon, TrashIcon} from '@core/icons/outline';
import Seo from '@components/common/Seo';
import Price from '@components/common/Price';

const MyFavorites = memo(() => {
    const t = useTrans();

    const [ref, observer] = useIntersection();
    const {removeFromFavorites} = useStore();

    const [isLoading, setIsLoading] = useState(false);
    const limit = useRef(32);
    const skip = useRef(0);
    const inProgress = useRef(false);
    const loadMoreRequested = useRef(false);
    const [products, setProducts] = useState<ProductListItem[]>([]);
    const [total, setTotal] = useState(0);
    const [hasNextPage, setHasNextPage] = useState(false);

    useEffect(() => {
        const getResult = async () => {
            const result = await jsonRequest({
                url: '/api/customers/favorite-products',
                method: 'POST',
                data: {
                    skip: skip.current,
                    limit: limit.current
                }
            });
            setProducts(result.data);
            setTotal(result.total);
            setHasNextPage(result.hasNextPage);
        };

        getResult();
    }, []);

    // Load more.
    useEffect(() => {
        if (observer?.isIntersecting) {
            if (inProgress.current) {
                loadMoreRequested.current = true;

                return;
            }

            inProgress.current = true;
            setIsLoading(true);
            skip.current += limit.current;

            setProducts(currentProducts =>
                currentProducts.concat(
                    [...Array(limit.current)].map(
                        () =>
                            ({
                                productId: randomId(16),
                                isFake: true
                            } as any)
                    )
                )
            );

            const load = async () => {
                const result = await jsonRequest({
                    url: '/api/customers/favorite-products',
                    method: 'POST',
                    data: {
                        skip: skip.current,
                        limit: limit.current
                    }
                });

                setProducts(currentProducts => {
                    currentProducts = currentProducts.filter(
                        // @ts-ignore
                        currentProduct => !currentProduct.isFake
                    );

                    return currentProducts.concat(result.data);
                });
                setTotal(result.total);
                setHasNextPage(result.hasNextPage);
                setIsLoading(false);
                inProgress.current = false;

                if (loadMoreRequested.current) {
                    loadMoreRequested.current = false;

                    if (result.hasNextPage) {
                        await load();
                    }
                }
            };

            load();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [observer]);

    // On Remove.
    const onRemove = useCallback(
        async (product: ProductListItem) => {
            if (inProgress.current) {
                return;
            }

            inProgress.current = true;
            setIsLoading(true);

            try {
                await removeFromFavorites({
                    id: product.productId,
                    name: product.name,
                    image:
                        Array.isArray(product.images) &&
                        product.images.length > 0
                            ? product.images[0]
                            : '/no-image.png',
                    price: product.salesPrice
                });
                setProducts(currentProducts => {
                    currentProducts = currentProducts.filter(
                        currentProduct =>
                            currentProduct.productId !== product.productId
                    );

                    return currentProducts;
                });

                const result = await jsonRequest({
                    url: '/api/customers/favorite-products',
                    method: 'POST',
                    data: {
                        skip: 0,
                        limit: 1
                    }
                });
                setTotal(result.total);
            } catch (error) {
                notification({
                    title: t('Error'),
                    description: t(
                        'We encountered an issue processing your request. Please retry later.'
                    ),
                    status: 'error'
                });
            }

            inProgress.current = false;
            setIsLoading(false);
        },
        [removeFromFavorites, t]
    );

    return (
        <>
            <Seo title={t('My Favorites')} />

            {total > 0 && (
                <div className="grid grid-cols-1 gap-4 border-gray-200  xl:grid-cols-2">
                    {products.map(item => {
                        return (
                            <div
                                key={item.productId}
                                className="group relative flex flex-col items-stretch rounded border p-4 shadow-sm transition duration-200  hover:shadow "
                            >
                                <div className="grid h-full w-full grid-cols-12 gap-0">
                                    <div
                                        className={cls(
                                            'relative col-span-3 flex h-28 '
                                        )}
                                    >
                                        <UiImage
                                            className="h-full max-h-28 !w-20 rounded  border lg:!w-28"
                                            src={
                                                item.images?.[0]
                                                    ? `${item.images?.[0]}?w=360&q=75`
                                                    : '/no-image.png'
                                            }
                                            alt={item.name}
                                            fill
                                            fit="cover"
                                            position="center"
                                        />
                                    </div>

                                    <div className="col-span-9 grid  gap-4">
                                        <div className="ml-2 flex  justify-between gap-2  ">
                                            <UiLink
                                                className="block  text-sm"
                                                href={`/${item.slug}`}
                                            >
                                                <h2 className="text-xs font-semibold lg:text-sm">
                                                    {item.name}
                                                </h2>
                                            </UiLink>
                                            <div
                                                className="flex h-6 w-6 cursor-pointer select-none items-center text-muted hover:text-danger-600"
                                                onClick={() => onRemove(item)}
                                            >
                                                <TrashIcon className="h-5 w-5" />
                                            </div>
                                        </div>
                                        <div className="ml-2 flex items-end justify-between  gap-2  ">
                                            <Price
                                                className="text-brand-black block text-sm font-normal  lg:text-2xl [&>span]:!text-primary-600 md:[&>span]:text-base "
                                                dontWrapDiscountedPrice={true}
                                                price={
                                                    typeof item.unDiscountedSalesPrice ===
                                                    'number'
                                                        ? item.unDiscountedSalesPrice
                                                        : item.salesPrice
                                                }
                                                discountedPrice={
                                                    item.salesPrice
                                                }
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>
            )}

            {total > 0 && !isLoading && hasNextPage && <span ref={ref}></span>}

            {total < 1 && !isLoading && (
                <div className="flex flex-1 flex-col items-center justify-center rounded border p-12">
                    <div className="flex h-24 w-24 items-center justify-center rounded-lg border border-dashed border-gray-500 text-gray-500">
                        <HeartIcon className="h-7 w-7" />
                    </div>

                    <h2 className="pt-12 text-center text-2xl font-semibold">
                        {t('No favorites found!')}
                    </h2>

                    <p className="px-10 pt-2 text-center text-muted">
                        {t('There are no favorites in your account.')}
                    </p>
                </div>
            )}
        </>
    );
});

if (isDev) {
    MyFavorites.displayName = 'MyFavorites';
}

export default MyFavorites;
