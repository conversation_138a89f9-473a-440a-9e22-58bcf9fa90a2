import {FC, memo, useCallback, useMemo, useState} from 'react';
import {useRouter} from 'next/router';
import {useSession} from 'next-auth/react';
import {isDev, pushIntoGTMDataLayer} from '@core/helpers';
import {useCart, useStore, useTrans} from '@core/hooks';
import {UiButton, UiTextarea, notification} from '@core/components/ui';
import {ArrowRightIcon} from '@core/icons/solid';
import CouponApplicationForm from '@components/common/CouponApplicationForm';
import {CartItem} from '@core/types';
import {useForm} from 'react-hook-form';

const Actions: FC = memo(() => {
    const t = useTrans();
    const router = useRouter();
    const {cart, updateCart} = useCart();
    const [isProceedToCheckOutInProgress, setIsProceedToCheckOutInProgress] =
        useState(false);
    const {currency} = useStore();
    const {data: session} = useSession();

    const {register, handleSubmit, watch, reset} = useForm({
        defaultValues: {
            orderNote: cart.orderNote || ''
        }
    });
    const orderNote = watch('orderNote');

    const isProceedToCheckOutEnabled = useMemo(
        () =>
            cart.items.filter(
                item => item.selected && !item.removed && item.quantity > 0
            ).length > 0 && cart.subTotal > 0,
        [cart]
    );

    const onProceedToCheckout = useCallback(async () => {
        for (const item of cart.items) {
            if (item.productStockQuantity < item.quantity) {
                notification({
                    title: t('Error'),
                    description: t(
                        'Please remove out of stock items from your cart!'
                    ),
                    status: 'error'
                });

                return;
            }
        }

        // ---------- Google Tag Manager ----------
        pushIntoGTMDataLayer({
            event: 'begin_checkout',
            data: {
                currency: currency.name === 'TL' ? 'TRY' : currency.name,
                value: cart.items
                    .map((item: CartItem) => item.price * item.quantity)
                    .reduce((a, b) => a + b, 0),
                items: cart.items.map(item => ({
                    item_id: item.productCode,
                    item_name: item.productName,
                    discount: item.discountedPrice
                        ? item.price - item.discountedPrice
                        : 0,
                    price: item.price,
                    item_brand: item.brandName,
                    item_category: item.productCategory,
                    quantity: item.quantity
                }))
            }
        });
        // ----------------------------------------

        setIsProceedToCheckOutInProgress(true);

        if (!!session) {
            await router.push(`/checkout?t=${Date.now()}`);
        } else {
            await router.push('/auth?redirect=checkout');
        }

        setIsProceedToCheckOutInProgress(false);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [router, cart]);

    const onSubmit = async () => {
        try {
            await updateCart({orderNote: orderNote});
        } catch (error: any) {}
    };

    return (
        <>
            <CouponApplicationForm disabled={!isProceedToCheckOutEnabled} />

            <div className=" mt-2">
                <UiTextarea
                    id="note"
                    {...register('orderNote')}
                    placeholder={t(
                        'You can write your note about your order here.'
                    )}
                    className="mt-2 max-h-32 resize-none text-sm"
                />
                {orderNote.length > 0 && (
                    <UiButton
                        onClick={() => reset({orderNote: ''})}
                        className="button-primary w-full"
                        variant="solid"
                    >
                        {t('Clear')}
                    </UiButton>
                )}
            </div>

            <UiButton
                className="mt-3 w-full"
                variant="solid"
                color="primary"
                size="xl"
                disabled={!isProceedToCheckOutEnabled}
                leftIcon={<ArrowRightIcon className="mr-2 h-5 w-5" />}
                loading={isProceedToCheckOutInProgress}
                onClick={() => {
                    handleSubmit(onSubmit)();
                    onProceedToCheckout();
                }}
            >
                {t('Proceed to Checkout')}
            </UiButton>
        </>
    );
});

if (isDev) {
    Actions.displayName = 'Actions';
}

export default Actions;
