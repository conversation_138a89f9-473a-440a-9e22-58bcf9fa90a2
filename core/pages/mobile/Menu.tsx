import {memo, useCallback, useMemo} from 'react';
import {useRouter} from 'next/router';
import {NavigationItem, Page} from '@core/types';
import {isDev} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {UiButton, UiLink} from '@core/components/ui';
import {ChevronRightIcon} from '@core/icons/outline';
import Seo from '@components/common/Seo';

type MobileMenuPageProps = {
    currentId?: string;
};

const MobileMenuPage: Page<MobileMenuPageProps> = memo(props => {
    const {currentId} = props;
    const router = useRouter();
    const t = useTrans();
    const {navigation} = useStore();
    const currentItem = useMemo(
        () =>
            navigation.find(navigationItem => navigationItem.id === currentId),
        [currentId, navigation]
    );
    const items = useMemo(() => {
        if (typeof currentItem === 'undefined') {
            return navigation.filter(
                navigationItem =>
                    navigationItem.showInMainMenu &&
                    navigationItem.slug.split('/').length == 1
            );
        }

        return navigation
            .filter(navigationItem => navigationItem.showInMainMenu)
            .filter(
                navigationItem =>
                    navigationItem.slug.includes(currentItem.slug) &&
                    navigationItem.slug.startsWith(currentItem.slug) &&
                    navigationItem.id !== currentItem.id &&
                    navigationItem.slug.split('/').length ==
                        currentItem.slug.split('/').length + 1
            );
    }, [navigation, currentItem]);

    const hasSubItems = useCallback(
        (item: NavigationItem) =>
            navigation.some(
                navigationItem =>
                    navigationItem.slug.includes(item.slug) &&
                    navigationItem.slug !== item.slug
            ),
        [navigation]
    );

    return (
        <>
            {currentItem ? (
                <Seo title={currentItem.name} />
            ) : (
                <Seo title={t('Menu')} />
            )}

            <div className="flex h-full w-full flex-col justify-between overflow-hidden">
                <div className="flex-1 overflow-y-auto">
                    <div className="divide-y divide-gray-200">
                        {items.map(item => {
                            if (
                                item.type === 'collection' ||
                                item.type === 'story' ||
                                item.type === 'slide'
                            )
                                return;

                            return (
                                <UiLink
                                    className="flex h-12 w-full items-center justify-between px-4"
                                    key={item.id}
                                    href={
                                        hasSubItems(item)
                                            ? `/mobile/menu/${item.id}`
                                            : item.href
                                    }
                                >
                                    <div>{item.name}</div>
                                    <ChevronRightIcon className="h-4 w-4 text-muted" />
                                </UiLink>
                            );
                        })}
                    </div>
                </div>

                {currentItem && currentItem.type == 'product-catalog' && (
                    <div className="border-t border-gray-200 p-4">
                        <UiButton
                            variant="solid"
                            color="primary"
                            className="w-full"
                            onClick={() => router.push(currentItem.href)}
                        >
                            {t('Show All Products')}
                        </UiButton>
                    </div>
                )}
            </div>
        </>
    );
});

if (isDev) {
    MobileMenuPage.displayName = 'MobileMenuPage';
}

export default MobileMenuPage;
