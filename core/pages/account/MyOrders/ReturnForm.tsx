import React, {FC, memo, useCallback, useMemo, useRef, useState} from 'react';
import {FormProvider, useForm} from 'react-hook-form';
import storeConfig from '~/store.config';
import {CartItem, Order} from '@core/types';
import {cls, isDev, jsonRequest, toLower} from '@core/helpers';
import {useStore, useTrans, useUI} from '@core/hooks';
import {
    UiAlert,
    UiButton,
    UiCheckbox,
    UiForm,
    UiImage,
    UiInput,
    UiTextarea
} from '@core/components/ui';
import Price from '@components/common/Price';

type ReturnFormProps = {
    items: CartItem[];
    order: Order;
    onClose: (
        items: CartItem[],
        result: {
            returnShipmentTackingCode: string;
            carrierName: string;
            storeCode: string;
            legalName: string;
        }
    ) => void;
};

const ReturnForm: FC<ReturnFormProps> = memo(({items, order, onClose}) => {
    const methods = useForm({
        defaultValues: {
            reasonId: '',
            description: '',
            selectedItems: items
                .filter(item => !item.isReturnRequested)
                .reduce<Record<string, any>>(
                    (acc, item) => ({...acc, [item.productId]: true}),
                    {}
                ),
            quantities: items
                .filter(item => !item.isReturnRequested)
                .reduce<Record<string, any>>(
                    (acc, item) => ({...acc, [item.productId]: item.quantity}),
                    {}
                )
        }
    });
    const {
        register,
        formState: {errors},
        watch,
        setValue
    } = methods;
    const {locale} = useStore();
    const t = useTrans();
    const {closeSideBar} = useUI();
    const [errorMessage, setErrorMessage] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [itemsToReturn, setItemsToReturn] = useState<CartItem[]>([]);
    const [returnOrderResult, setReturnOrderResult] = useState<{
        returnShipmentTackingCode: string;
        carrierName: string;
        storeCode: string;
        legalName: string;
    }>();
    const inProgress = useRef(false);

    const reasonId = watch('reasonId');
    const selectedItems = watch('selectedItems');
    const quantities = watch('quantities');

    const isDescriptionShown = useMemo(() => {
        const option = order.returnReasonOptions.find(
            option => option.value === reasonId
        );

        return !!option && toLower(option.label) === toLower(t('Other'));
    }, [order.returnReasonOptions, t, reasonId]);

    const onSubmit = useCallback(
        async (data: Record<string, any>) => {
            if (inProgress.current) return;

            inProgress.current = true;
            setIsLoading(true);

            try {
                if (!Object.values(selectedItems).some(item => item)) {
                    throw new Error(
                        t('Please select at least one item to return.')
                    );
                }

                const itemsToReturn = items.filter(
                    item => selectedItems[item.productId]
                );

                if (!itemsToReturn.every(item => item.canBeReturned)) {
                    throw new Error(
                        t('Returns are no longer accepted for this order.')
                    );
                }

                const areQuantitesValid = itemsToReturn.every(item => {
                    const selectedQuantity = quantities[item.productId];
                    if (
                        typeof selectedQuantity !== 'number' ||
                        selectedQuantity < 1 ||
                        selectedQuantity > item.quantity
                    ) {
                        return false;
                    }
                    return true;
                });
                if (!areQuantitesValid) {
                    throw new Error(
                        t(
                            'Please ensure the quantity entered for each item is valid. The quantity must be at least 1 and not exceed the maximum quantity purchased for the item.'
                        )
                    );
                }

                const result = await jsonRequest({
                    url: '/api/customers/create-return-order',
                    method: 'POST',
                    data: {
                        locale,
                        shipmentTackingCode: items[0].shipmentTackingCode ?? '',
                        returnItems: itemsToReturn.map(item => ({
                            productId: item.productId,
                            quantity:
                                quantities[item.productId] ?? item.quantity
                        })),
                        reasonId: data.reasonId,
                        description: data.description,
                        orderId: order.orderId,
                        isIntegrationOrder: order.isIntegrationOrder ?? false
                    }
                });

                setItemsToReturn(itemsToReturn);
                setReturnOrderResult(result);
            } catch (error: any) {
                setErrorMessage(error.message);
            }

            setIsLoading(false);
            inProgress.current = false;
        },
        [
            order.orderId,
            order.isIntegrationOrder,
            items,
            locale,
            selectedItems,
            quantities,
            t
        ]
    );

    return (
        <>
            {returnOrderResult ? (
                <div className="my-4 flex flex-col px-4 pb-4 xl:my-2 xl:px-6 xl:pb-6">
                    <div className="mb-2 font-semibold">
                        {t('Return Created')}
                    </div>
                    <div className="mb-4">
                        {t(
                            'Your return has been successfully created. You can deliver the product you want to return to the nearest {carrierName} branch with the cargo order code {returnShipmentTackingCode}.',
                            {
                                returnShipmentTackingCode:
                                    returnOrderResult.returnShipmentTackingCode ||
                                    returnOrderResult.storeCode,
                                carrierName: returnOrderResult.carrierName
                            }
                        )}
                    </div>
                    {returnOrderResult.legalName && (
                        <>
                            <div className="mb-2 font-semibold">
                                {t('Company Title Information')}
                            </div>
                            <div className="mb-4">
                                {returnOrderResult.legalName}
                            </div>
                        </>
                    )}
                    {returnOrderResult.storeCode && (
                        <>
                            <div className="mb-2 font-semibold">
                                {t('Cargo Store Code')}
                            </div>
                            <div className="mb-4">
                                {returnOrderResult.storeCode}
                            </div>
                        </>
                    )}
                    <UiButton
                        className="mt-2 w-full"
                        variant="solid"
                        color="primary"
                        size="xl"
                        onClick={() => {
                            onClose(itemsToReturn, returnOrderResult);
                            closeSideBar();
                        }}
                    >
                        {t('Close')}
                    </UiButton>
                </div>
            ) : (
                <FormProvider {...methods}>
                    <UiForm
                        className="mb-4 flex flex-col px-4 pb-4 xl:mb-2 xl:px-6 xl:pb-6"
                        onSubmit={methods.handleSubmit(onSubmit)}
                    >
                        <div className="mb-4 text-sm max-xl:mt-4">
                            {t('Please mark the items you wish to return.')}
                        </div>

                        {!!errorMessage && (
                            <UiAlert className="mb-4" color="danger">
                                {t(errorMessage)}
                            </UiAlert>
                        )}

                        <div className="space-y-3">
                            {items
                                .filter(item => !item.isReturnRequested)
                                .map(item => (
                                    <UiForm.Control
                                        key={item.productId}
                                        className="flex border-b pb-3 last:border-b-0 last:pb-0 [&>label]:w-full [&_span]:w-full"
                                    >
                                        <UiCheckbox
                                            {...register(
                                                `selectedItems.${item.productId}`
                                            )}
                                            defaultChecked
                                            onChange={() =>
                                                setValue(
                                                    `selectedItems.${item.productId}`,
                                                    !selectedItems[
                                                        item.productId
                                                    ]
                                                )
                                            }
                                        >
                                            <div className="flex">
                                                <div
                                                    className={cls(
                                                        'h-24 flex-shrink-0 overflow-hidden rounded',
                                                        {
                                                            'w-18':
                                                                storeConfig
                                                                    .catalog
                                                                    .productImageShape ===
                                                                'rectangle',
                                                            'w-24':
                                                                storeConfig
                                                                    .catalog
                                                                    .productImageShape !==
                                                                'rectangle'
                                                        }
                                                    )}
                                                >
                                                    <UiImage
                                                        className="h-full w-full rounded"
                                                        src={item.productImage}
                                                        alt={item.productName}
                                                        width={
                                                            storeConfig.catalog
                                                                .productImageShape ===
                                                            'rectangle'
                                                                ? 144
                                                                : 196
                                                        }
                                                        height={196}
                                                        fit="cover"
                                                        position="center"
                                                    />
                                                </div>

                                                <div className="ml-4 flex flex-1 flex-col justify-between">
                                                    <h3 className="text-sm">
                                                        {item.productName}
                                                    </h3>
                                                    <div className="my-2 flex flex-wrap items-center gap-2">
                                                        <p>
                                                            {t(
                                                                'Quantity to be returned'
                                                            )}
                                                            :
                                                        </p>
                                                        <div className="flex items-center gap-2">
                                                            <UiInput
                                                                {...register(
                                                                    `quantities.${item.productId}`,
                                                                    {
                                                                        required:
                                                                            true,
                                                                        valueAsNumber:
                                                                            true
                                                                    }
                                                                )}
                                                                className="w-14 md:w-16"
                                                                type="number"
                                                                size="sm"
                                                                min={1}
                                                                max={
                                                                    item.quantity
                                                                }
                                                            />
                                                            <p className="text-base">
                                                                /{' '}
                                                                {item.quantity}
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <Price
                                                        className="text-sm font-bold"
                                                        price={item.price}
                                                    />
                                                </div>
                                            </div>
                                        </UiCheckbox>
                                    </UiForm.Control>
                                ))}
                        </div>

                        <div className="mt-4 border-t border-gray-200 pt-4">
                            <UiForm.Field
                                label={t('Return reason')}
                                error={
                                    errors.reasonId &&
                                    errors.reasonId.type === 'required'
                                        ? t('{label} is required', {
                                              label: t('Return reason')
                                          })
                                        : undefined
                                }
                                {...register('reasonId', {
                                    required: true
                                })}
                                defaultValue="other"
                                selection
                            >
                                {order.returnReasonOptions.map(option => (
                                    <option
                                        key={option.value}
                                        value={option.value}
                                    >
                                        {option.label}
                                    </option>
                                ))}
                            </UiForm.Field>

                            {isDescriptionShown && (
                                <UiForm.Control className="mt-3">
                                    <UiTextarea
                                        placeholder={t('Description')}
                                        className="h-24"
                                        maxLength={1000}
                                        {...register('description', {
                                            required: false,
                                            maxLength: 1000
                                        })}
                                    />
                                </UiForm.Control>
                            )}

                            <UiButton
                                className="mt-2 w-full"
                                variant="solid"
                                color="primary"
                                size="xl"
                                loading={isLoading}
                                disabled={isLoading}
                            >
                                {t('Create Return')}
                            </UiButton>
                        </div>
                    </UiForm>
                </FormProvider>
            )}
        </>
    );
});

if (isDev) {
    ReturnForm.displayName = 'ReturnForm';
}

export default ReturnForm;
