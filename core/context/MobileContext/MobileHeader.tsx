import {FC, memo, useCallback} from 'react';
import {useRouter} from 'next/router';
import siteLogo from '@assets/images/common/site-logo.png';
import storeConfig from '~/store.config';
import {NavigationItem} from '@core/types';
import {cls, isDev} from '@core/helpers';
import {useMobile} from '@core/hooks';
import {UiImage, UiLink} from '@core/components/ui';
import {ArrowLeftIcon, ChartTreeMapIcon, SearchIcon} from '@core/icons/outline';
import MobileMenuSlider from './MobileMenuSlider';
import MobileSearchBox from './MobileSearchBox';

type MobileHeaderProps = {
    title?: string;
    shouldShowScrollNav?: boolean;
    mainNavItems?: NavigationItem[];
};

const MobileHeader: FC<MobileHeaderProps> = memo(
    ({shouldShowScrollNav, title, mainNavItems}) => {
        const router = useRouter();
        const {mobileTitle, setIsMobileSearchShown} = useMobile();

        const onGoBack = useCallback(() => router.back(), [router]);

        return (
            <div
                className={cls(
                    'fixed left-0 right-0 top-0 z-[48] w-full border-b border-gray-100 bg-white xl:hidden',
                    {'flex h-mobile-header': !shouldShowScrollNav}
                )}
            >
                {shouldShowScrollNav ? (
                    <>
                        <div className="container flex w-full items-center justify-between border-b border-gray-100 py-3">
                            <UiLink href="/" aria-label="Logo">
                                <UiImage
                                    src={siteLogo}
                                    alt={storeConfig.title}
                                    width={parseFloat(
                                        storeConfig.theme.mobileLogoWidth.replace(
                                            'px',
                                            ''
                                        )
                                    )}
                                    height={parseFloat(
                                        storeConfig.theme.mobileLogoHeight.replace(
                                            'px',
                                            ''
                                        )
                                    )}
                                    priority
                                />
                            </UiLink>

                            <UiLink href="/mobile/menu">
                                <ChartTreeMapIcon className="h-5 w-5" />
                            </UiLink>
                        </div>

                        <MobileSearchBox />
                        <MobileMenuSlider mainNavItems={mainNavItems} />
                    </>
                ) : (
                    <div className="container flex w-full flex-1 items-center space-x-3">
                        <button
                            className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 shadow transition active:opacity-30"
                            onClick={onGoBack}
                        >
                            <ArrowLeftIcon className="h-4 w-4" />
                        </button>

                        <div className="min-w-0 flex-1">
                            <div className="w-full truncate font-semibold">
                                {!!title ? title : mobileTitle}
                            </div>
                        </div>

                        <button
                            className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 shadow transition active:opacity-30"
                            onClick={() => setIsMobileSearchShown(true)}
                        >
                            <SearchIcon className="h-4 w-4" />
                        </button>
                    </div>
                )}
            </div>
        );
    }
);

if (isDev) {
    MobileHeader.displayName = 'MobileHeader';
}

export default MobileHeader;
