import {FC, memo, useMemo} from 'react';
import {useRouter} from 'next/router';
import {NavigationItem} from '@core/types';
import {cls, isDev} from '@core/helpers';
import {useStore} from '@core/hooks';
import {UiLink} from '@core/components/ui';
import {StoreIcon} from '@core/icons/solid';

interface MobileMenuSliderProps {
    mainNavItems: NavigationItem[] | undefined;
}

const MobileMenuSlider: FC<MobileMenuSliderProps> = memo(({mainNavItems}) => {
    const {slug} = useStore();
    const router = useRouter();

    const isHome = useMemo(() => router.asPath === '/', [router.asPath]);

    return Array.isArray(mainNavItems) && mainNavItems.length > 0 ? (
        <ul className="hide-scrollbar container relative z-40 -mb-[1px] flex snap-x snap-mandatory items-center gap-4 overflow-x-auto overflow-y-hidden whitespace-nowrap shadow-sm xl:hidden">
            <li>
                <UiLink className={cls({'text-primary-600': isHome})} href="/">
                    <StoreIcon className="h-5 w-5" />
                </UiLink>
            </li>

            {mainNavItems?.map(item => {
                const lowerCaseCategoryName = item.name
                    .toLowerCase()
                    .split(' ')
                    .map(s => s.charAt(0).toUpperCase() + s.substring(1))
                    .join(' ')
                    .replace(/i/g, 'ı');

                return (
                    <li
                        key={item.id}
                        className={cls(
                            'min-w-fit overflow-hidden py-3 text-sm font-medium focus-within:text-primary-600 hover:text-primary-600',
                            {
                                'mobile-menu-underline text-primary-600':
                                    item.slug === slug
                            }
                        )}
                    >
                        <UiLink href={item.href}>
                            {lowerCaseCategoryName}
                        </UiLink>
                    </li>
                );
            })}
        </ul>
    ) : null;
});

if (isDev) {
    MobileMenuSlider.displayName = 'MobileMenuSlider';
}

export default MobileMenuSlider;
