import {
    createContext,
    FC,
    memo,
    PropsWithChildren,
    useEffect,
    useMemo,
    useState
} from 'react';
import {useRouter} from 'next/router';
import {cls, isDev} from '@core/helpers';
import {useStore, useViewportSize} from '@core/hooks';
import MobileHeader from './MobileHeader';
import MobileSearch from './MobileSearch';
import MobileTabBar from './MobileTabBar';
import {MobileContextType} from './types';
import {Filter} from '@core/types';

export const MobileContext = createContext<MobileContextType>(null as any);

if (isDev) {
    MobileContext.displayName = 'MobileContext';
}

export const MobileProvider: FC<PropsWithChildren<unknown>> = memo(
    ({children}) => {
        const router = useRouter();
        const {width: viewportWidth} = useViewportSize();
        const [mobileTitle, setMobileTitle] = useState('');
        const [isMobileSearchShown, setIsMobileSearchShown] = useState(false);
        const {navigation} = useStore();
        const [isMobile, setIsMobile] = useState(false);
        const [selectedFilter, setSelectedFilter] = useState<Filter>();
        const isMobileViewport = useMemo(
            () => viewportWidth > 0 && viewportWidth < 1024,
            [viewportWidth]
        );

        useEffect(() => {
            if (isMobileViewport) {
                setIsMobile(true);
            }
        }, [isMobileViewport]);

        const activeTab = useMemo(() => {
            const path = router.asPath;
            if (path.includes('/mobile/menu')) {
                return 'menu';
            } else if (path.includes('/mobile/my-cart')) {
                return 'my-cart';
            } else if (path.includes('/my-favorites')) {
                return 'my-favorites';
            } else if (
                path.includes('/mobile/my-account') ||
                (path !== '/account/my-favorites' &&
                    (path.includes('/auth') || path.includes('/account/')))
            ) {
                return 'my-account';
            }

            return 'store';
        }, [router.asPath]);

        const value: any = useMemo(
            () => ({
                mobileTitle,
                isMobileSearchShown,
                activeTab,
                isMobile,
                setMobileTitle,
                setIsMobileSearchShown,
                selectedFilter,
                setSelectedFilter
            }),
            [
                mobileTitle,
                isMobileSearchShown,
                activeTab,
                isMobile,
                setIsMobileSearchShown,
                selectedFilter,
                setSelectedFilter
            ]
        );

        const mainNavItems = useMemo(
            () =>
                (navigation || []).filter(item => {
                    return (
                        item.depth === 0 &&
                        item.type === 'product-catalog' &&
                        item.showInMainMenu
                    );
                }),
            // eslint-disable-next-line react-hooks/exhaustive-deps
            []
        );

        const isHome = useMemo(() => router.asPath === '/', [router.asPath]);

        const shouldShowScrollNav =
            mainNavItems.find(item => `/${item.slug}` === router.asPath) !==
                undefined || isHome;

        return (
            <MobileContext.Provider value={value}>
                <div
                    className={cls(
                        'relative h-full w-full overflow-hidden pb-mobile-tab-bar xl:pb-0 xl:pt-0',
                        {'pt-mobile-header': !shouldShowScrollNav},
                        {'pt-[155px]': shouldShowScrollNav}
                    )}
                >
                    <MobileHeader
                        shouldShowScrollNav={shouldShowScrollNav}
                        mainNavItems={mainNavItems}
                    />

                    <div className="content-wrapper h-full w-full overflow-y-auto">
                        {children}
                    </div>

                    <MobileTabBar />
                </div>

                <MobileSearch />
            </MobileContext.Provider>
        );
    }
);

if (isDev) {
    MobileProvider.displayName = 'MobileProvider';
}
