import {FC, memo} from 'react';
import storeConfig from '~/store.config';
import {isDev} from '@core/helpers';
import {useCart, useIOSDevice, useMobile, useTrans} from '@core/hooks';
import {UiLink} from '@core/components/ui';
import {
    BagIcon,
    ChartTreeMapIcon,
    HeartIcon,
    StoreIcon,
    UserCircleIcon
} from '@core/icons/outline';
import {
    BagIcon as BagSolidIcon,
    ChartTreeMapIcon as ChartTreeMapSolidIcon,
    HeartIcon as HeartSolidIcon,
    StoreIcon as StoreSolidIcon,
    UserCircleIcon as UserCircleSolidIcon
} from '@core/icons/solid';

const MobileTabBar: FC = memo(() => {
    const t = useTrans();
    const {itemCount: cartItemsCount} = useCart();
    const {activeTab} = useMobile();
    const {isIOSDevice} = useIOSDevice();

    return (
        <div
            className="fixed bottom-0 left-0 z-[48] block w-full select-none border-t border-gray-200 bg-white shadow xl:hidden"
            style={{
                height: `${
                    isIOSDevice
                        ? storeConfig.theme.iosTabBarHeight
                        : storeConfig.theme.mobileTabBarHeight
                }`
            }}
        >
            <div className="container h-full">
                <div className="flex h-full items-start justify-between">
                    <UiLink
                        className="flex flex-1 flex-col items-center justify-between py-2.5"
                        href="/"
                    >
                        {activeTab === 'store' ? (
                            <>
                                <StoreSolidIcon className="h-5 w-5 text-primary-600" />
                                <div className="h-[10px] pt-1 text-[10px] leading-[10px] text-primary-600">
                                    {t('Store')}
                                </div>
                            </>
                        ) : (
                            <>
                                <StoreIcon className="h-5 w-5" />
                                <div className="h-[10px] pt-1 text-[10px] leading-[10px] text-gray-500">
                                    {t('Store')}
                                </div>
                            </>
                        )}
                    </UiLink>

                    <UiLink
                        className="flex flex-1 flex-col items-center justify-between py-2.5"
                        href="/mobile/menu"
                    >
                        {activeTab === 'menu' ? (
                            <>
                                <ChartTreeMapSolidIcon className="h-5 w-5 text-primary-600" />
                                <div className="h-[10px] pt-1 text-[10px] leading-[10px] text-primary-600">
                                    {t('Menu')}
                                </div>
                            </>
                        ) : (
                            <>
                                <ChartTreeMapIcon className="h-5 w-5" />
                                <div className="h-[10px] pt-1 text-[10px] leading-[10px] text-gray-500">
                                    {t('Menu')}
                                </div>
                            </>
                        )}
                    </UiLink>

                    <UiLink
                        className="flex flex-1 flex-col items-center py-2.5"
                        href="/mobile/my-cart"
                    >
                        <div className="relative flex flex-1 flex-col items-center justify-between">
                            {activeTab === 'my-cart' ? (
                                <>
                                    <BagSolidIcon className="h-5 w-5 text-primary-600" />
                                    <div className="h-[10px] pt-1 text-[10px] leading-[10px] text-primary-600">
                                        {t('My Cart')}
                                    </div>
                                </>
                            ) : (
                                <>
                                    <BagIcon className="h-5 w-5" />
                                    <div className="h-[10px] pt-1 text-[10px] leading-[10px] text-gray-500">
                                        {t('My Cart')}
                                    </div>
                                </>
                            )}

                            {cartItemsCount > 0 && (
                                <span
                                    className="
                                    absolute -top-1.5 right-1 flex
                                    items-center justify-center rounded-full bg-red-600 text-[9px] font-medium text-white
                                    "
                                    style={{
                                        paddingLeft: '2px',
                                        paddingRight: '2px',
                                        minWidth: '1rem',
                                        minHeight: '1rem'
                                    }}
                                >
                                    {cartItemsCount}
                                </span>
                            )}
                        </div>
                    </UiLink>

                    <UiLink
                        className="flex flex-1 flex-col items-center justify-between py-2.5"
                        href="/account/my-favorites"
                    >
                        {activeTab === 'my-favorites' ? (
                            <>
                                <HeartSolidIcon className="h-5 w-5 text-primary-600" />
                                <div className="h-[10px] pt-1 text-[10px] leading-[10px] text-primary-600">
                                    {t('My Favorites')}
                                </div>
                            </>
                        ) : (
                            <>
                                <HeartIcon className="h-5 w-5" />
                                <div className="h-[10px] pt-1 text-[10px] leading-[10px] text-gray-500">
                                    {t('My Favorites')}
                                </div>
                            </>
                        )}
                    </UiLink>

                    <UiLink
                        className="flex flex-1 flex-col items-center justify-between py-2.5"
                        href="/mobile/my-account"
                    >
                        {activeTab === 'my-account' ? (
                            <>
                                <UserCircleSolidIcon className="h-5 w-5 text-primary-600" />
                                <div className="h-[10px] pt-1 text-[10px] leading-[10px] text-primary-600">
                                    {t('My Account')}
                                </div>
                            </>
                        ) : (
                            <>
                                <UserCircleIcon className="h-5 w-5" />
                                <div className="h-[10px] pt-1 text-[10px] leading-[10px] text-gray-500">
                                    {t('My Account')}
                                </div>
                            </>
                        )}
                    </UiLink>
                </div>
            </div>
        </div>
    );
});

if (isDev) {
    MobileTabBar.displayName = 'MobileTabBar';
}

export default MobileTabBar;
