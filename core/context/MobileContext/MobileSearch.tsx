import {
    ChangeEventHandler,
    FC,
    Fragment,
    KeyboardEventHandler,
    memo,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react';
import {useRouter} from 'next/router';
import storeConfig from '~/store.config';
import {ProductSearchResultItem} from '@core/types';
import {cls, debounce, isDev, jsonRequest, toUpper, trim} from '@core/helpers';
import {useMobile, useTrans} from '@core/hooks';
import {
    UiButton,
    UiImage,
    UiInput,
    UiLink,
    UiSpinner,
    UiTransition
} from '@core/components/ui';
import {ChevronRightIcon, SearchIcon} from '@core/icons/outline';
import EmptyResult from '@components/common/EmptyResult';

const SearchResultItem: FC<{
    item: ProductSearchResultItem;
    onClick: () => void;
}> = memo(({item, onClick}) => {
    const t = useTrans();
    const {setIsMobileSearchShown} = useMobile();
    const description = useMemo(() => {
        if (item.type === 'product') {
            return t('Product');
        } else if (item.type === 'brand') {
            return t('Brand');
        } else if (item.type === 'navigation') {
            return t('Category');
        }

        return '';
    }, [t, item.type]);

    return (
        <UiLink
            className="flex w-full items-center p-4 focus:outline-none"
            href={`/${item.slug}`}
            onClick={() => {
                setIsMobileSearchShown(false);
                onClick();
            }}
        >
            {typeof item.image === 'string' && item.image.length > 0 ? (
                <div
                    className={cls(
                        'h-12 flex-shrink-0 overflow-hidden rounded',
                        {
                            'w-9':
                                storeConfig.catalog.productImageShape ===
                                'rectangle',
                            'w-12':
                                storeConfig.catalog.productImageShape !==
                                'rectangle'
                        }
                    )}
                >
                    <UiImage
                        className="h-full w-full rounded"
                        src={item.image}
                        alt={item.name}
                        width={
                            storeConfig.catalog.productImageShape ===
                            'rectangle'
                                ? 36
                                : 48
                        }
                        height={48}
                        fit="cover"
                        position="center"
                        quality={75}
                    />
                </div>
            ) : (
                <div
                    className="
                        flex h-12 w-12 items-center justify-center bg-primary-50 text-xl
                        font-semibold text-primary-600
                        "
                >
                    {item.name
                        .split(' ')
                        .slice(0, 2)
                        .map(s => toUpper(s)[0])
                        .join('')}
                </div>
            )}

            <div className="ml-3 flex flex-1 flex-col justify-between">
                <div className="mb-1.5 text-sm font-medium">{item.name}</div>
                <div className="text-xs text-muted">{description}</div>
            </div>

            <div className="ml-3 flex items-center">
                <ChevronRightIcon className="h-4 w-4 text-muted" />
            </div>
        </UiLink>
    );
});

if (isDev) {
    SearchResultItem.displayName = 'SearchResultItem';
}

const MobileSearch: FC = memo(() => {
    const router = useRouter();
    const t = useTrans();
    const {isMobileSearchShown, setIsMobileSearchShown} = useMobile();
    const inputRef = useRef<HTMLInputElement | null>(null);
    const [searchResult, setSearchResult] = useState<ProductSearchResultItem[]>(
        []
    );
    const [searchQuery, setSearchQuery] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    const searchDebounced = useRef(
        debounce(
            async (query: string) => {
                query = trim(query);

                try {
                    if (!(query === '' || query.length < 2)) {
                        const result = await jsonRequest({
                            url: '/api/catalog/search',
                            method: 'POST',
                            data: {searchQuery: query}
                        });

                        setSearchResult(() =>
                            result.map((item: ProductSearchResultItem) => ({
                                ...item,
                                name: !!item.brandName
                                    ? `${item.brandName} ${item.name}`
                                    : item.name
                            }))
                        );
                    } else {
                        setSearchResult([]);
                    }
                } catch (error: any) {}

                setIsLoading(false);
            },
            250,
            {leading: false, trailing: true}
        )
    );
    const onSearch: ChangeEventHandler<HTMLInputElement> = useCallback(e => {
        const query = e.target.value;

        setIsLoading(true);
        searchDebounced.current(query);
        setSearchQuery(query);
    }, []);

    // Focus input on mount.
    useEffect(() => {
        if (isMobileSearchShown) {
            setTimeout(() => {
                if (inputRef.current !== null) {
                    inputRef.current?.focus();
                }
            }, 150);

            /* searchDebounced.current(''); */
        }
    }, [isMobileSearchShown]);

    const onPopularSearchClick = useCallback((query: string) => {
        setIsLoading(true);

        query = trim(query);

        (async () => {
            try {
                const result = await jsonRequest({
                    url: '/api/catalog/search',
                    method: 'POST',
                    data: {searchQuery: query}
                });

                setSearchResult(() =>
                    result.map((item: ProductSearchResultItem) => ({
                        ...item,
                        name: !!item.brandName
                            ? `${item.brandName} ${item.name}`
                            : item.name
                    }))
                );
            } catch (error: any) {}

            setIsLoading(false);
            setSearchQuery(query);
            // Focus input on click
            inputRef.current?.focus();
        })();
    }, []);

    const onPopularCategoryClick = useCallback(() => {
        setIsMobileSearchShown(false);
        setSearchQuery('');
    }, [setIsMobileSearchShown]);
    const onSearchItemClick = useCallback(() => {
        (async () => {
            try {
                if (!(searchQuery === '' || searchQuery.length < 2)) {
                    await jsonRequest({
                        url: '/api/catalog/save-popular-search',
                        method: 'POST',
                        data: {searchQuery: searchQuery}
                    });
                }
            } catch (error: any) {}
        })();

        setSearchQuery('');
        setSearchResult([]);
        setIsMobileSearchShown(false);
    }, [searchQuery, setIsMobileSearchShown]);

    const onGoToSearchDetail = useCallback(async () => {
        setIsLoading(true);

        await router.push(
            `/search?${new URLSearchParams({query: searchQuery}).toString()}`
        );
        setSearchQuery('');
        setSearchResult([]);
        setIsMobileSearchShown(false);
        setIsLoading(false);
    }, [router, searchQuery, setIsMobileSearchShown]);
    const onKeyDown: KeyboardEventHandler<HTMLInputElement> = useCallback(
        e => {
            if (e.code === 'Enter' || e.code === 'NumpadAdd') {
                onGoToSearchDetail();
            }
        },
        [onGoToSearchDetail]
    );

    return (
        <UiTransition
            show={isMobileSearchShown}
            as={Fragment}
            enter="transition duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
        >
            <div className="fixed inset-0 z-[9999] flex h-full w-full flex-col bg-white">
                <div
                    className="
                        flex h-mobile-header w-full select-none items-center border-b border-gray-200 bg-white
                        pl-3 pr-4
                        "
                    style={{flex: '0 0 60px'}}
                >
                    <UiInput.Group className="flex-1">
                        <UiInput.LeftElement>
                            <SearchIcon className="h-4 w-4 text-muted" />
                        </UiInput.LeftElement>

                        <UiInput
                            ref={inputRef}
                            placeholder={t(
                                'Search product, category or brand..'
                            )}
                            className="border-gray-100 bg-gray-100 transition focus:bg-white"
                            value={searchQuery}
                            onChange={onSearch}
                            onKeyDown={onKeyDown}
                        />

                        {isLoading && (
                            <UiInput.RightElement>
                                <UiSpinner size="sm" />
                            </UiInput.RightElement>
                        )}
                    </UiInput.Group>

                    <div className="ml-4">
                        <button
                            className="font-semibold text-primary-600 transition active:opacity-30"
                            onClick={() => setIsMobileSearchShown(false)}
                        >
                            {t('Close')}
                        </button>
                    </div>
                </div>

                <div className="flex-1">
                    {searchResult.length > 0 ? (
                        <div className="relative h-full w-full overflow-hidden">
                            <div className="absolute inset-0 overflow-y-auto pb-[77px]">
                                <div className="divide-y divide-gray-200">
                                    {searchResult.map(item => (
                                        <SearchResultItem
                                            key={item.id}
                                            item={item}
                                            onClick={onSearchItemClick}
                                        />
                                    ))}
                                </div>
                            </div>

                            <div className="absolute bottom-0 z-10 h-[77px] w-full border-t border-gray-200 bg-white p-4">
                                <UiButton
                                    variant="solid"
                                    color="primary"
                                    size="lg"
                                    className="w-full"
                                    loading={isLoading}
                                    disabled={isLoading}
                                    onClick={onGoToSearchDetail}
                                >
                                    {t('SHOW ALL THE SEARCH RESULTS')}
                                </UiButton>
                            </div>
                        </div>
                    ) : !isLoading && searchQuery.length >= 2 ? (
                        <div className="flex h-full w-full flex-col items-center justify-center px-10">
                            <div
                                className="
                            flex h-12 w-12 items-center justify-center
                            rounded-lg border border-dashed border-gray-500 text-gray-500
                            "
                            >
                                <SearchIcon className="h-4 w-4" />
                            </div>

                            <h2 className="pt-5 text-center font-semibold text-base">
                                {t('No result found!')}
                            </h2>

                            <p className="pt-2.5 text-center text-muted">
                                {t(
                                    'Please change your search criteria and try again.'
                                )}
                            </p>
                        </div>
                    ) : (
                        <EmptyResult
                            onPopularSearchClick={onPopularSearchClick}
                            onPopularCategoryClick={onPopularCategoryClick}
                        />
                    )}
                </div>
            </div>
        </UiTransition>
    );
});

if (isDev) {
    MobileSearch.displayName = 'MobileSearch';
}

export default MobileSearch;
