import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useMobile, useTrans} from '@core/hooks';
import {UiInput} from '@core/components/ui';
import {SearchIcon} from '@core/icons/outline';

const MobileSearchBox: FC = memo(() => {
    const t = useTrans();
    const {setIsMobileSearchShown} = useMobile();

    return (
        <div className="container mt-3 block xl:hidden">
            <UiInput.Group size="lg" className="flex-1">
                <UiInput.LeftElement>
                    <SearchIcon className="h-4 w-4 text-muted" />
                </UiInput.LeftElement>

                <UiInput
                    placeholder={t('Search product, category or brand..')}
                    className="border-gray-100 bg-gray-100 focus:!border-gray-200 focus:ring-0"
                    readOnly
                    onFocus={() => setIsMobileSearchShown(true)}
                />
            </UiInput.Group>
        </div>
    );
});

if (isDev) {
    MobileSearchBox.displayName = 'MobileSearchBox';
}

export default MobileSearchBox;
