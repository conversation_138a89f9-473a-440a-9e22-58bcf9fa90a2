import {
    createContext,
    FC,
    memo,
    PropsWithChildren,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react';
import {useSession} from 'next-auth/react';
import {Cart, CartItem} from '@core/types';
import {isDev, jsonRequest, pushIntoGTMDataLayer} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {notification} from '@core/components/ui';

type OrderType = {
    orderNote: string;
};

type CartContextType = {
    cart: Cart;
    itemCount: number;
    productCount: number;
    isLoading: boolean;

    setCart: (cart: Cart) => void;
    addItem: (item: CartItem) => Promise<boolean>;
    updateItem: (item: CartItem) => Promise<boolean>;
    removeItem: (item: CartItem) => Promise<void>;
    removeItems: () => Promise<void>;
    refreshCart: () => Promise<void>;
    applyCouponCode: (couponCode: string) => Promise<void>;
    updateCart: ({orderNote}: OrderType) => Promise<void>;
};

export const CartContext = createContext<CartContextType>(null as any);

if (isDev) {
    CartContext.displayName = 'CartContext';
}

type CartProviderProps = {
    cart?: Cart;
};

export const CartProvider: FC<PropsWithChildren<CartProviderProps>> = memo(
    ({cart: initialCart, children}) => {
        const [cart, setCart] = useState<Cart>(() => {
            if (initialCart) {
                return initialCart;
            } else {
                return {
                    status: 'draft',
                    step: 'information',
                    subTotal: 0,
                    discountTotal: 0,
                    taxTotal: 0,
                    deliveryTotal: 0,
                    cashOnDeliveryServiceFee: 0,
                    grandTotal: 0,
                    itemCount: 0,
                    productCount: 0,
                    discounts: [],
                    items: [],
                    deliveryType: 'standard',
                    deliveryOptionId: '',
                    cardBrand: 'none',
                    installmentCount: 1,
                    dueDifference: 0,
                    minimumPurchaseAmountForFreeShipping: 0
                };
            }
        });

        const [isLoading, setIsLoading] = useState(false);
        const inProgress = useRef(false);
        const isInitial = useRef(true);

        const {currency} = useStore();
        const t = useTrans();

        const itemCount = useMemo(() => cart.itemCount, [cart]);
        const productCount = useMemo(() => cart.productCount, [cart]);

        const {data: session} = useSession();

        useEffect(() => {
            setIsLoading(true);

            (async () => {
                const newCart = await jsonRequest({
                    url: '/api/cart',
                    method: 'POST',
                    data: {}
                });

                setCart(newCart);
                setIsLoading(false);
                isInitial.current = false;
            })();
        }, [session?.user?.id]);

        const addItem = useCallback(async (item: CartItem) => {
            if (inProgress.current) {
                return;
            }

            let result = false;
            inProgress.current = true;
            setIsLoading(true);

            try {
                const newCart = await jsonRequest({
                    url: '/api/cart/add-item',
                    method: 'POST',
                    data: {item}
                });

                setCart(newCart);

                result = true;
            } catch (error: any) {
                notification({
                    title: t('Error'),
                    description: error.message,
                    status: 'error'
                });
            }

            setIsLoading(false);
            inProgress.current = false;

            return result;
            // eslint-disable-next-line
        }, []);
        const updateItem = useCallback(async (item: CartItem) => {
            if (inProgress.current) {
                return;
            }

            let result = false;
            inProgress.current = true;
            setIsLoading(true);

            try {
                const newCart = await jsonRequest({
                    url: '/api/cart/update-item',
                    method: 'POST',
                    data: {item}
                });

                setCart(newCart);

                result = true;
            } catch (error: any) {
                notification({
                    title: t('Error'),
                    description: error.message,
                    status: 'error'
                });
            }

            setIsLoading(false);
            inProgress.current = false;

            return result;
            // eslint-disable-next-line
        }, []);
        const removeItem = useCallback(async (item: CartItem) => {
            if (inProgress.current) {
                return;
            }

            inProgress.current = true;
            setIsLoading(true);

            try {
                const newCart = await jsonRequest({
                    url: '/api/cart/remove-item',
                    method: 'POST',
                    data: {item}
                });

                // ---------- Google Tag Manager ----------
                pushIntoGTMDataLayer({
                    event: 'remove_from_cart',
                    data: {
                        currency:
                            currency.name === 'TL' ? 'TRY' : currency.name,
                        value: item.price * item.quantity,
                        items: [
                            {
                                item_id: item.productCode,
                                item_name: item.productName,
                                discount: item.discountedPrice
                                    ? item.price - item.discountedPrice
                                    : 0,
                                price: item.price,
                                item_brand: item.brandName,
                                item_category: item.productCategory,
                                quantity: item.quantity
                            }
                        ]
                    }
                });
                // ----------------------------------------

                setCart(newCart);
            } catch (error: any) {
                notification({
                    title: t('Error'),
                    description: error.message,
                    status: 'error'
                });
            }

            setIsLoading(false);
            inProgress.current = false;
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, []);
        const removeItems = useCallback(async () => {
            if (inProgress.current) {
                return;
            }

            inProgress.current = true;
            setIsLoading(true);

            try {
                const newCart = await jsonRequest({
                    url: '/api/cart/remove-items',
                    method: 'POST',
                    data: {}
                });

                setCart(newCart);
            } catch (error: any) {
                notification({
                    title: t('Error'),
                    description: error.message,
                    status: 'error'
                });
            }

            setIsLoading(false);
            inProgress.current = false;
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, []);

        const refreshCart = useCallback(async () => {
            const newCart = await jsonRequest({
                url: '/api/cart',
                method: 'POST',
                data: {}
            });

            setCart(newCart);
        }, []);

        const applyCouponCode = useCallback(async (couponCode: string) => {
            if (inProgress.current) {
                return;
            }

            inProgress.current = true;
            setIsLoading(true);

            try {
                const newCart = await jsonRequest({
                    url: '/api/cart/apply-coupon-code',
                    method: 'POST',
                    data: {couponCode}
                });

                setCart(newCart);
                setIsLoading(false);
                inProgress.current = false;
            } catch (error) {
                setIsLoading(false);
                inProgress.current = false;

                throw error;
            }
        }, []);

        const updateCart = useCallback(async (uploadPayload: OrderType) => {
            if (inProgress.current) {
                return;
            }

            inProgress.current = true;
            setIsLoading(true);

            try {
                const newCart = await jsonRequest({
                    url: '/api/cart/update-cart',
                    method: 'POST',
                    data: uploadPayload
                });

                setCart(newCart);
                setIsLoading(false);
                inProgress.current = false;
            } catch (error) {
                setIsLoading(false);
                inProgress.current = false;

                throw error;
            }
        }, []);

        const value: any = useMemo(
            () => ({
                cart,
                itemCount,
                productCount,
                isLoading,

                setCart,
                addItem,
                updateItem,
                removeItem,
                removeItems,
                refreshCart,
                applyCouponCode,
                updateCart
            }),
            [
                cart,
                itemCount,
                productCount,
                isLoading,
                addItem,
                updateItem,
                removeItem,
                removeItems,
                refreshCart,
                applyCouponCode,
                updateCart
            ]
        );

        return (
            <CartContext.Provider value={value}>
                {children}
            </CartContext.Provider>
        );
    }
);

if (isDev) {
    CartProvider.displayName = 'CartProvider';
}
